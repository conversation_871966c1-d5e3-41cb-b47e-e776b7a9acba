import streamlit as st
import pandas as pd
import json
from datetime import datetime
from utils.session_state import (
    get_session_state, set_session_state, add_dataset, 
    set_current_table, get_dataset_names, update_clean_data
)
from utils.data_utils import (
    fetch_api_all, fetch_notion_database, apply_clean_code,
    df_to_excel_bytes, parse_json_input, extract_database_id_from_url
)
from utils.chart_utils import create_chart_from_config
from my_pages.dashboard import add_chart_to_dashboard


def show():
    """显示数据分析工作台页面"""
    st.title("🔧 数据分析工作台")
    
    # 数据表选择器
    dataset_names = get_dataset_names()
    if dataset_names:
        col1, col2, col3 = st.columns([3, 1.2, 1])
        with col1:
            current_table = get_session_state("current_table")
            selected_table = st.selectbox(
                "选择数据表",
                options=dataset_names,
                index=dataset_names.index(current_table) if current_table in dataset_names else 0,
                key="table_selector"
            )
            if selected_table != current_table:
                set_current_table(selected_table)
                st.rerun()
        
        with col2:
            # 添加一些垂直间距以对齐按钮
            st.write("")
            if st.button("🗑️ 删除当前表", use_container_width=True, help="删除当前选中的数据表"):
                if len(dataset_names) > 1:
                    datasets = get_session_state("datasets", {})
                    if selected_table in datasets:
                        del datasets[selected_table]
                        set_session_state("datasets", datasets)
                        # 选择第一个剩余的表
                        remaining_tables = [t for t in dataset_names if t != selected_table]
                        if remaining_tables:
                            set_current_table(remaining_tables[0])
                        else:
                            set_current_table(None)
                        st.rerun()
                else:
                    st.error("至少需要保留一个数据表")
        
        with col3:
            if selected_table:
                dataset = get_session_state("datasets", {}).get(selected_table, {})
                raw_df = dataset.get("raw")
                if raw_df is not None:
                    st.metric("数据行数", f"{len(raw_df):,}")
    
    # 顶部操作栏
    col_l, col_r = st.columns([1, 2], vertical_alignment="center")
    with col_l:
        st.caption("支持 CSV、Excel、API、Notion 数据导入，代码清洗与可视化")
    with col_r:
        show_quick_update_buttons()
    
    # 主要功能标签页
    tabs = st.tabs(["📥 数据导入", "🛠️ 数据清洗", "📊 可视化分析", "⚙️ 配置管理"])
    
    with tabs[0]:
        show_data_import()
    
    with tabs[1]:
        show_data_cleaning()
    
    with tabs[2]:
        show_visualization()
    
    with tabs[3]:
        show_config_management()

def show_quick_update_buttons():
    """显示快速更新按钮"""
    current_table = get_session_state("current_table")
    if not current_table:
        return
    
    dataset = get_session_state("datasets", {}).get(current_table, {})
    source_info = dataset.get("source_info", {})
    source_type = source_info.get("type")
    
    if source_type == "api":
        if st.button("🔄 一键更新（API）", use_container_width=True):
            with st.spinner("正在从 API 拉取数据并应用清洗..."):
                try:
                    api_config = get_session_state("api_config")
                    df_raw, _metas = fetch_api_all(api_config)
                    
                    # 更新数据集
                    datasets = get_session_state("datasets", {})
                    datasets[current_table]["raw"] = df_raw
                    datasets[current_table]["source_info"]["import_time"] = datetime.now().isoformat()
                    
                    # 应用清洗代码
                    clean_code = get_session_state("clean_code", "")
                    if clean_code.strip():
                        df_clean, logs, err = apply_clean_code(df_raw, clean_code)
                        if err:
                            st.error(f"清洗代码执行失败：{err}")
                            if logs.strip():
                                with st.expander("查看执行日志"):
                                    st.code(logs)
                            datasets[current_table]["clean"] = df_raw.copy()
                        else:
                            datasets[current_table]["clean"] = df_clean
                            st.success(f"更新完成：{len(df_clean):,} 行")
                    else:
                        datasets[current_table]["clean"] = df_raw.copy()
                        st.success(f"更新完成：{len(df_raw):,} 行（未设置清洗代码）")
                    
                    set_session_state("datasets", datasets)
                    # 更新向后兼容变量
                    set_current_table(current_table)
                except Exception as e:
                    st.error(f"API 更新失败：{e}")
    
    elif source_type == "notion":
        if st.button("🔄 一键更新（Notion）", use_container_width=True):
            with st.spinner("正在从 Notion 拉取数据并应用清洗..."):
                try:
                    notion_config = get_session_state("notion_config")
                    df_raw = fetch_notion_database(notion_config)
                    
                    # 更新数据集
                    datasets = get_session_state("datasets", {})
                    datasets[current_table]["raw"] = df_raw
                    datasets[current_table]["source_info"]["import_time"] = datetime.now().isoformat()
                    
                    # 应用清洗代码
                    clean_code = get_session_state("clean_code", "")
                    if clean_code.strip():
                        df_clean, logs, err = apply_clean_code(df_raw, clean_code)
                        if err:
                            st.error(f"清洗代码执行失败：{err}")
                            if logs.strip():
                                with st.expander("查看执行日志"):
                                    st.code(logs)
                            datasets[current_table]["clean"] = df_raw.copy()
                        else:
                            datasets[current_table]["clean"] = df_clean
                            st.success(f"更新完成：{len(df_clean):,} 行")
                    else:
                        datasets[current_table]["clean"] = df_raw.copy()
                        st.success(f"更新完成：{len(df_raw):,} 行（未设置清洗代码）")
                    
                    set_session_state("datasets", datasets)
                    # 更新向后兼容变量
                    set_current_table(current_table)
                except Exception as e:
                    st.error(f"Notion 更新失败：{e}")

def show_data_import():
    """显示数据导入界面"""
    st.subheader("📥 数据导入")
    
    # 数据源选择
    try:
        from notion_client import Client
        NOTION_AVAILABLE = True
    except ImportError:
        NOTION_AVAILABLE = False
    
    if NOTION_AVAILABLE:
        source_options = ["本地文件（CSV/Excel）", "CRM/API", "Notion 数据库"]
    else:
        source_options = ["本地文件（CSV/Excel）", "CRM/API"]
    
    source_type = st.radio("选择数据源", options=source_options, horizontal=True)
    
    if not NOTION_AVAILABLE and "Notion" in str(source_type):
        st.error("❌ Notion 功能不可用。请安装 notion-client: `pip install notion-client`")
    
    if source_type.startswith("本地文件"):
        show_file_import()
    elif source_type.startswith("CRM/API"):
        show_api_import()
    elif source_type.startswith("Notion") and NOTION_AVAILABLE:
        show_notion_import()

def show_file_import():
    """显示文件导入界面"""
    set_session_state("source_type", "file")
    
    file = st.file_uploader("上传 CSV 或 Excel 文件", type=["csv", "xlsx", "xls"])
    if file is not None:
        file_name = file.name.lower()
        
        if file_name.endswith(".csv"):
            set_session_state("file_type", "csv")
            set_session_state("excel_sheet", None)
            
            # CSV参数设置
            csv_sep = st.text_input("CSV 分隔符", value=get_session_state("csv_sep", ","), max_chars=3)
            set_session_state("csv_sep", csv_sep)
            
            # 表名输入
            table_name = st.text_input("数据表名称", value=file.name.replace('.csv', ''), key="csv_table_name")
            
            if st.button("导入数据", key="import_csv"):
                # 读取CSV文件，使用自动编码检测
                encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp936', 'latin1']
                success = False
                
                for encoding in encodings_to_try:
                    try:
                        file.seek(0)
                        df = pd.read_csv(file, sep=csv_sep, encoding=encoding)
                        source_info = {
                            "type": "csv",
                            "filename": file.name,
                            "encoding": encoding,
                            "separator": csv_sep,
                            "import_time": datetime.now().isoformat()
                        }
                        add_dataset(table_name, df, source_info)
                        if encoding == 'utf-8':
                            st.success(f"CSV 导入成功：{len(df):,} 行，{len(df.columns)} 列")
                        else:
                            st.success(f"CSV 导入成功（自动检测编码为 {encoding}）：{len(df):,} 行，{len(df.columns)} 列")
                        st.dataframe(df.head(50), use_container_width=True)
                        success = True
                        break
                    except (UnicodeDecodeError, pd.errors.EmptyDataError, pd.errors.ParserError):
                        continue
                
                if not success:
                    st.error("无法读取CSV文件，请检查文件格式或编码")
        
        else:  # Excel文件
            set_session_state("file_type", "excel")
            xls = pd.ExcelFile(file)
            sheet = st.selectbox("选择工作表", options=xls.sheet_names)
            set_session_state("excel_sheet", sheet)
            
            # 表名输入
            table_name = st.text_input("数据表名称", value=f"{file.name.split('.')[0]}_{sheet}", key="excel_table_name")
            
            if st.button("导入数据", key="import_excel"):
                try:
                    df = pd.read_excel(file, sheet_name=sheet)
                    source_info = {
                        "type": "excel",
                        "filename": file.name,
                        "sheet_name": sheet,
                        "import_time": datetime.now().isoformat()
                    }
                    add_dataset(table_name, df, source_info)
                    st.success(f"Excel 导入成功：{len(df):,} 行，{len(df.columns)} 列（工作表：{sheet}）")
                    st.dataframe(df.head(50), use_container_width=True)
                except Exception as e:
                    st.error(f"读取 Excel 失败：{e}")

def show_api_import():
    """显示API导入界面"""
    set_session_state("source_type", "api")
    api_cfg = get_session_state("api_config")
    
    with st.container(border=True):
        st.markdown("#### 基础配置")
        c1, c2, c3 = st.columns([2, 1, 1])
        with c1:
            api_cfg["url"] = st.text_input("URL", value=api_cfg.get("url", ""), placeholder="https://example.com/api/endpoint")
        with c2:
            api_cfg["method"] = st.selectbox("HTTP 方法", options=["GET", "POST"], index=0 if api_cfg.get("method", "GET") == "GET" else 1)
        with c3:
            api_cfg["timeout"] = st.number_input("超时（秒）", min_value=1, max_value=300, value=int(api_cfg.get("timeout", 30)))
        
        c4, c5 = st.columns(2)
        with c4:
            api_cfg["auth_type"] = st.selectbox("认证方式", ["None", "Bearer", "Basic"], index=["None", "Bearer", "Basic"].index(api_cfg.get("auth_type", "None")))
            if api_cfg["auth_type"] == "Bearer":
                api_cfg["auth_bearer_token"] = st.text_input("Bearer Token", type="password", value=api_cfg.get("auth_bearer_token", ""))
            elif api_cfg["auth_type"] == "Basic":
                api_cfg["auth_basic_user"] = st.text_input("用户名", value=api_cfg.get("auth_basic_user", ""))
                api_cfg["auth_basic_pass"] = st.text_input("密码", type="password", value=api_cfg.get("auth_basic_pass", ""))
        with c5:
            api_cfg["records_path"] = st.text_input("数据路径（可选，点号分隔）", value=api_cfg.get("records_path", ""), placeholder="例如 data.items")
    
    with st.container(border=True):
        st.markdown("#### 请求细节（JSON 形式）")
        c6, c7 = st.columns(2)
        with c6:
            api_cfg["headers_json"] = st.text_area("Headers JSON", value=api_cfg.get("headers_json", "{}"), height=120)
            api_cfg["params_json"] = st.text_area("Query Params JSON", value=api_cfg.get("params_json", "{}"), height=120)
        with c7:
            if api_cfg.get("method", "GET") == "POST":
                api_cfg["body_json"] = st.text_area("Body JSON（POST）", value=api_cfg.get("body_json", "{}"), height=245)
            else:
                st.caption("GET 请求无需 Body")
    
    with st.container(border=True):
        st.markdown("#### 分页（可选）")
        api_cfg["enable_pagination"] = st.checkbox("启用分页", value=bool(api_cfg.get("enable_pagination", False)))
        if api_cfg["enable_pagination"]:
            c8, c9, c10 = st.columns(3)
            with c8:
                api_cfg["page_param"] = st.text_input("页码参数名", value=api_cfg.get("page_param", "page"))
                api_cfg["page_start"] = st.number_input("起始页码", value=int(api_cfg.get("page_start", 1)), step=1)
            with c9:
                api_cfg["page_size_param"] = st.text_input("每页大小参数名", value=api_cfg.get("page_size_param", "page_size"))
                api_cfg["page_size_value"] = st.number_input("每页大小", value=int(api_cfg.get("page_size_value", 100)), step=1)
            with c10:
                api_cfg["max_pages"] = st.number_input("最大页数", value=int(api_cfg.get("max_pages", 10)), min_value=1, step=1)
    
    set_session_state("api_config", api_cfg)
    
    # 表名输入
    table_name = st.text_input("数据表名称", value="API数据", key="api_table_name")
    
    if st.button("拉取数据", type="primary"):
        with st.spinner("请求 API 中..."):
            try:
                df_raw, metas = fetch_api_all(api_cfg)
                source_info = {
                    "type": "api",
                    "url": api_cfg.get("url", ""),
                    "method": api_cfg.get("method", "GET"),
                    "import_time": datetime.now().isoformat()
                }
                add_dataset(table_name, df_raw, source_info)
                st.success(f"API 导入成功：{len(df_raw):,} 行，{len(df_raw.columns)} 列")
                st.dataframe(df_raw.head(50), use_container_width=True)
            except Exception as e:
                st.error(f"API 请求失败：{e}")

def show_notion_import():
    """显示Notion导入界面"""
    set_session_state("source_type", "notion")
    notion_cfg = get_session_state("notion_config")
    
    with st.container(border=True):
        st.markdown("#### 🗃️ Notion 数据库配置")
        
        col1, col2 = st.columns([2, 1])
        with col1:
            notion_cfg["token"] = st.text_input(
                "Notion Token",
                value=notion_cfg.get("token", ""),
                type="password",
                placeholder="ntn_...",
                help="在 Notion 设置中创建集成并获取 Token"
            )
        with col2:
            notion_cfg["max_results"] = st.number_input(
                "最大结果数",
                min_value=1,
                max_value=1000,
                value=int(notion_cfg.get("max_results", 100)),
                step=10
            )
        
        # 添加导入方式选择
        import_method = st.radio(
            "导入方式",
            ["📝 Database ID", "🔗 数据库链接"],
            horizontal=True,
            help="选择通过Database ID或数据库链接导入数据"
        )
        
        if import_method == "📝 Database ID":
            notion_cfg["database_id"] = st.text_input(
                "Database ID",
                value=notion_cfg.get("database_id", ""),
                placeholder="例如：a8aec43384f447ed84390e8e42c2e089",
                help="数据库 URL 中的 32 位字符串"
            )
        else:
            database_url = st.text_input(
                "数据库链接",
                value="",
                placeholder="https://www.notion.so/your-workspace/database-name-a8aec43384f447ed84390e8e42c2e089",
                help="粘贴完整的Notion数据库URL"
            )
            
            if database_url:
                try:
                    extracted_id = extract_database_id_from_url(database_url)
                    notion_cfg["database_id"] = extracted_id
                    st.success(f"✅ 已提取Database ID: {extracted_id}")
                except Exception as e:
                    st.error(f"❌ URL解析失败: {e}")
                    notion_cfg["database_id"] = ""
    
    set_session_state("notion_config", notion_cfg)
    
    # 表名输入
    table_name = st.text_input("数据表名称", value="Notion数据", key="notion_table_name")
    
    if st.button("📊 获取 Notion 数据", type="primary"):
        with st.spinner("正在从 Notion 获取数据..."):
            try:
                df_raw = fetch_notion_database(notion_cfg)
                source_info = {
                    "type": "notion",
                    "database_id": notion_cfg.get("database_id", ""),
                    "import_time": datetime.now().isoformat()
                }
                add_dataset(table_name, df_raw, source_info)
                st.success(f"Notion 导入成功：{len(df_raw):,} 行，{len(df_raw.columns)} 列")
                st.dataframe(df_raw.head(50), use_container_width=True)
            except Exception as e:
                st.error(f"Notion 数据获取失败：{e}")

def show_data_cleaning():
    """显示数据清洗界面"""
    st.subheader("🛠️ 数据清洗")
    
    current_table = get_session_state("current_table")
    if not current_table:
        st.info("请先在\"数据导入\"页导入数据。")
        return
    
    dataset = get_session_state("datasets", {}).get(current_table, {})
    raw_df = dataset.get("raw")
    if raw_df is None:
        st.info("当前表没有原始数据。")
        return
    
    # 显示常用清洗操作
    show_common_cleaning_operations()
    
    # 显示高级清洗（代码模式）
    show_advanced_cleaning()
    
    # 显示数据预览
    show_data_preview()

def show_common_cleaning_operations():
    """显示常用数据清洗操作"""
    st.markdown("#### 🛠️ 常用数据清洗操作")
    
    # 获取当前数据
    current_table = get_session_state("current_table")
    dataset = get_session_state("datasets", {}).get(current_table, {})
    clean_df = dataset.get("clean")
    raw_df = dataset.get("raw")
    
    if clean_df is not None and not clean_df.empty:
        current_df = clean_df
    elif raw_df is not None and not raw_df.empty:
        current_df = raw_df
    else:
        st.warning("无可用数据")
        return
    
    cols = list(current_df.columns)
    
    with st.container(border=True):
        st.markdown("##### 列操作")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # 删除空格操作
            st.markdown("**删除空格**")
            selected_col_trim = st.selectbox("选择列（删除空格）", options=["请选择"] + cols, key="trim_col")
            trim_type = st.radio("删除类型", ["前后空格", "所有空格", "仅前空格", "仅后空格"], key="trim_type", horizontal=True)
            
            if st.button("🧹 执行删除空格", key="btn_trim"):
                if selected_col_trim != "请选择":
                    try:
                        df_work = current_df.copy()
                        if trim_type == "前后空格":
                            df_work[selected_col_trim] = df_work[selected_col_trim].astype(str).str.strip()
                        elif trim_type == "所有空格":
                            df_work[selected_col_trim] = df_work[selected_col_trim].astype(str).str.replace(' ', '', regex=False)
                        elif trim_type == "仅前空格":
                            df_work[selected_col_trim] = df_work[selected_col_trim].astype(str).str.lstrip()
                        elif trim_type == "仅后空格":
                            df_work[selected_col_trim] = df_work[selected_col_trim].astype(str).str.rstrip()
                        
                        update_clean_data(current_table, df_work)
                        st.success(f"已对列 '{selected_col_trim}' 执行{trim_type}操作")
                        st.rerun()
                    except Exception as e:
                        st.error(f"操作失败：{e}")
                else:
                    st.warning("请先选择要操作的列")
        
        with col2:
            # 删除重复行
            st.markdown("**删除重复行**")
            duplicate_cols = st.multiselect("基于哪些列判断重复（空=全部列）", options=cols, key="dup_cols")
            keep_option = st.selectbox("保留", ["第一个", "最后一个"], key="keep_dup")
            
            if st.button("🗑️ 删除重复行", key="btn_dedup"):
                try:
                    df_work = current_df.copy()
                    subset_cols = duplicate_cols if duplicate_cols else None
                    keep_val = "first" if keep_option == "第一个" else "last"
                    
                    original_count = len(df_work)
                    df_work = df_work.drop_duplicates(subset=subset_cols, keep=keep_val)
                    removed_count = original_count - len(df_work)
                    
                    update_clean_data(current_table, df_work)
                    st.success(f"删除了 {removed_count} 行重复数据，剩余 {len(df_work)} 行")
                    st.rerun()
                except Exception as e:
                    st.error(f"操作失败：{e}")
        
        with col3:
            # 删除空值行
            st.markdown("**删除空值行**")
            null_cols = st.multiselect("基于哪些列判断空值（空=任意列）", options=cols, key="null_cols")
            null_how = st.selectbox("删除条件", ["任意列为空", "所有列都为空"], key="null_how")
            
            if st.button("🚫 删除空值行", key="btn_dropna"):
                try:
                    df_work = current_df.copy()
                    subset_cols = null_cols if null_cols else None
                    how_val = "any" if null_how == "任意列为空" else "all"
                    
                    original_count = len(df_work)
                    df_work = df_work.dropna(subset=subset_cols, how=how_val)
                    removed_count = original_count - len(df_work)
                    
                    set_session_state("clean_df", df_work)
                    st.success(f"删除了 {removed_count} 行空值数据，剩余 {len(df_work)} 行")
                    st.rerun()
                except Exception as e:
                    st.error(f"操作失败：{e}")

def show_advanced_cleaning():
    """显示高级清洗（代码模式）"""
    st.markdown("---")
    st.markdown("#### 📝 高级清洗（代码模式）")
    st.markdown("你可以在下面输入自定义 pandas 清洗代码。")
    
    with st.expander("查看示例清洗代码", expanded=False):
        st.code(
            """# 示例1：保留列、重命名、类型转换
# df 为输入的 DataFrame，写法1：直接在 df 上修改
# df = df[['日期','渠道','订单金额']].rename(columns={'订单金额':'sales'}).copy()
# df['日期'] = pd.to_datetime(df['日期'])
# df['month'] = df['日期'].dt.to_period('M').astype(str)

# 示例2：写函数并返回 result
# def transform(df):
#     out = df.copy()
#     out = out.dropna(subset=['订单金额'])
#     out['订单金额'] = out['订单金额'].astype(float)
#     return out
# result = transform(df)

# 示例3：分组汇总
# clean_df = df.groupby('渠道', as_index=False)['订单金额'].sum()""",
            language="python"
        )
    
    clean_code = st.text_area(
        "在此输入清洗代码（我会用 exec 执行）",
        value=get_session_state("clean_code", ""),
        height=260,
        placeholder="示例：df = df.dropna().rename(columns={'old':'new'})"
    )
    set_session_state("clean_code", clean_code)
    
    if st.button("运行清洗代码", type="primary"):
        with st.spinner("执行清洗代码..."):
            current_table = get_session_state("current_table")
            dataset = get_session_state("datasets", {}).get(current_table, {})
            raw_df = dataset.get("raw")
            if raw_df is None:
                st.error("没有原始数据可供清洗")
                return
            
            new_df, logs, err = apply_clean_code(raw_df, clean_code)
            if err:
                st.error(err)
            else:
                update_clean_data(current_table, new_df)
                st.success(f"清洗成功：{len(new_df):,} 行，{len(new_df.columns)} 列")
            if logs and logs.strip():
                with st.expander("查看执行日志"):
                    st.code(logs)

def show_data_preview():
    """显示数据预览"""
    c1, c2 = st.columns(2)
    with c1:
        st.markdown("##### 原始数据预览")
        current_table = get_session_state("current_table")
        dataset = get_session_state("datasets", {}).get(current_table, {})
        raw_df = dataset.get("raw")
        if raw_df is not None:
            st.dataframe(raw_df.head(50), use_container_width=True)
        else:
            st.info("无原始数据")
    
    with c2:
        st.markdown("##### 清洗结果预览")
        clean_df = dataset.get("clean")
        if clean_df is not None:
            st.dataframe(clean_df.head(50), use_container_width=True)
        else:
            st.info("尚未生成清洗结果。点击\"运行清洗代码\"执行。")

def show_visualization():
    """显示可视化分析界面"""
    st.subheader("📊 可视化分析")
    
    # 获取活跃的数据框
    current_table = get_session_state("current_table")
    if not current_table:
        st.info("请先选择数据表。")
        return
    
    dataset = get_session_state("datasets", {}).get(current_table, {})
    clean_df = dataset.get("clean")
    raw_df = dataset.get("raw")
    
    if clean_df is not None and not clean_df.empty:
        active_df = clean_df
    elif raw_df is not None and not raw_df.empty:
        active_df = raw_df
    else:
        active_df = None
    
    if active_df is None or active_df.empty:
        st.info("没有可视化的数据。请先导入并（可选）清洗。")
        return
    
    df = active_df
    cols = list(df.columns)
    
    cfg = get_session_state("viz_config")
    
    left, right = st.columns([1, 2])
    with left:
        cfg["chart_type"] = st.selectbox("图表类型", options=["折线图", "柱状图", "散点图", "直方图", "箱线图"], index=["折线图","柱状图","散点图","直方图","箱线图"].index(cfg.get("chart_type", "折线图")))
        cfg["x"] = st.selectbox("X 轴", options=[None] + cols, index=(cols.index(cfg.get("x")) + 1) if cfg.get("x") in cols else 0)
        cfg["y"] = st.selectbox("Y 轴", options=[None] + cols, index=(cols.index(cfg.get("y")) + 1) if cfg.get("y") in cols else 0)
        cfg["color"] = st.selectbox("颜色/分组", options=[None] + cols, index=(cols.index(cfg.get("color")) + 1) if cfg.get("color") in cols else 0)
        cfg["size"] = st.selectbox("点大小（散点）", options=[None] + cols, index=(cols.index(cfg.get("size")) + 1) if cfg.get("size") in cols else 0)
        cfg["aggregate"] = st.selectbox("聚合函数（柱状）", options=["none", "sum", "mean", "median", "count", "min", "max"], index=["none", "sum", "mean", "median", "count", "min", "max"].index(cfg.get("aggregate", "none")))
        cfg["tooltip"] = st.multiselect("提示字段", options=cols, default=[cfg["x"], cfg["y"]] if cfg.get("x") in cols and cfg.get("y") in cols else [])
        cfg["sample_rows"] = st.number_input("采样行数（提升渲染速度）", min_value=100, max_value=200000, value=int(cfg.get("sample_rows", 5000)), step=100)
        
        # 添加到看板按钮
        st.markdown("---")
        chart_title = st.text_input("图表标题", value=f"{cfg.get('chart_type', '图表')} - {cfg.get('x', 'X')} vs {cfg.get('y', 'Y')}")
        chart_description = st.text_area("图表描述", value="", height=68)
        
        if st.button("📌 添加到看板", type="primary", use_container_width=True):
            try:
                chart_idx = add_chart_to_dashboard(cfg.copy(), chart_title, chart_description)
                st.success(f"图表已添加到看板！图表编号：{chart_idx + 1}")
            except Exception as e:
                st.error(f"添加到看板失败：{e}")
    
    set_session_state("viz_config", cfg)
    
    with right:
        try:
            chart = create_chart_from_config(df, cfg)
            if chart:
                st.altair_chart(chart, use_container_width=True)
            else:
                st.warning("无法创建图表，请检查配置")
            
            # 下载选项
            st.markdown("##### 📥 下载选项")
            col_d1, col_d2, col_d3, col_d4 = st.columns(4)
            
            with col_d1:
                if st.button("💾 保存图表", help="点击后请在图表上右键选择'保存图片'", use_container_width=True):
                    st.info("💡 请在上方图表上右键点击，选择'保存图片'来下载PNG格式的图表")
            
            with col_d2:
                # 下载图表数据为CSV
                chart_data = df
                if cfg.get("sample_rows") and len(df) > cfg.get("sample_rows", 5000):
                    chart_data = df.sample(n=cfg.get("sample_rows", 5000), random_state=42)
                
                csv_chart_data = chart_data.to_csv(index=False).encode("utf-8-sig")
                st.download_button(
                    label="📊 图表数据CSV",
                    data=csv_chart_data,
                    file_name="chart_data.csv",
                    mime="text/csv",
                    help="下载当前图表使用的数据",
                    use_container_width=True
                )
            
            with col_d3:
                # 下载完整数据为CSV
                csv_full_data = df.to_csv(index=False).encode("utf-8-sig")
                data_label = "📄 完整数据CSV" if clean_df is not None else "📄 原始数据CSV"
                file_name = "cleaned_data.csv" if clean_df is not None else "raw_data.csv"
                
                st.download_button(
                    label=data_label,
                    data=csv_full_data,
                    file_name=file_name,
                    mime="text/csv",
                    use_container_width=True
                )
            
            with col_d4:
                # 下载完整数据为Excel
                excel_full_data = df_to_excel_bytes(df)
                data_label = "📗 完整数据Excel" if clean_df is not None else "📗 原始数据Excel"
                file_name = "cleaned_data.xlsx" if clean_df is not None else "raw_data.xlsx"
                
                st.download_button(
                    label=data_label,
                    data=excel_full_data,
                    file_name=file_name,
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    use_container_width=True
                )
        
        except Exception as e:
            st.error(f"绘图失败：{e}")
        
        with st.expander("数据预览", expanded=False):
            st.dataframe(df.head(100), use_container_width=True)

def show_config_management():
    """显示配置管理界面"""
    st.subheader("⚙️ 配置管理")
    
    st.info("💡 数据下载功能已移至【可视化分析】标签页，在那里可以下载图表、图表数据、完整CSV和Excel文件。")
    
    st.markdown("#### 📋 保存/加载分析配置")
    st.markdown("你可以保存当前的API配置、清洗代码和可视化设置，以便下次使用。")
    
    # 显示当前配置概览
    with st.expander("📖 查看当前配置概览", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**数据源配置**")
            source_type = get_session_state("source_type", "未设置")
            st.write(f"- 数据源类型: {source_type}")
            
            if source_type == "api":
                api_url = get_session_state("api_config", {}).get("url", "未设置")
                st.write(f"- API URL: {api_url[:50]}..." if len(api_url) > 50 else f"- API URL: {api_url}")
            elif source_type == "notion":
                notion_db_id = get_session_state("notion_config", {}).get("database_id", "未设置")
                st.write(f"- Notion DB: {notion_db_id[:20]}..." if len(notion_db_id) > 20 else f"- Notion DB: {notion_db_id}")
            
            clean_code = get_session_state("clean_code", "")
            st.write(f"- 清洗代码: {'已设置' if clean_code.strip() else '未设置'}")
        
        with col2:
            st.markdown("**数据状态**")
            current_table = get_session_state("current_table")
            if current_table:
                dataset = get_session_state("datasets", {}).get(current_table, {})
                raw_df = dataset.get("raw")
                clean_df = dataset.get("clean")
                
                if raw_df is not None:
                    st.write(f"- 原始数据: {len(raw_df):,} 行 × {len(raw_df.columns)} 列")
                else:
                    st.write("- 原始数据: 未导入")
                
                if clean_df is not None:
                    st.write(f"- 清洗数据: {len(clean_df):,} 行 × {len(clean_df.columns)} 列")
                else:
                    st.write("- 清洗数据: 未生成")
            else:
                st.write("- 当前表: 未选择")
    
    # 配置保存和加载
    col_save, col_load = st.columns(2)
    
    with col_save:
        st.markdown("##### 💾 保存配置")
        cfg_bundle = {
            "api_config": get_session_state("api_config", {}),
            "notion_config": get_session_state("notion_config", {}),
            "clean_code": get_session_state("clean_code", ""),
            "viz_config": get_session_state("viz_config", {}),
        }
        cfg_json = json.dumps(cfg_bundle, ensure_ascii=False, indent=2)
        st.download_button(
            "📥 下载配置文件",
            data=cfg_json.encode("utf-8"),
            file_name="analysis_config.json",
            mime="application/json",
            use_container_width=True,
            help="保存当前的API配置、清洗代码和可视化设置"
        )
    
    with col_load:
        st.markdown("##### 📂 加载配置")
        uploaded_cfg = st.file_uploader(
            "选择配置文件",
            type=["json"],
            key="cfg_uploader",
            help="上传之前保存的配置文件来恢复设置"
        )
        
        if uploaded_cfg is not None:
            try:
                loaded = json.load(uploaded_cfg)
                if "api_config" in loaded:
                    current_api_config = get_session_state("api_config", {})
                    set_session_state("api_config", {**current_api_config, **loaded["api_config"]})
                if "notion_config" in loaded:
                    current_notion_config = get_session_state("notion_config", {})
                    set_session_state("notion_config", {**current_notion_config, **loaded["notion_config"]})
                if "clean_code" in loaded:
                    set_session_state("clean_code", loaded["clean_code"] or "")
                if "viz_config" in loaded:
                    current_viz_config = get_session_state("viz_config", {})
                    set_session_state("viz_config", {**current_viz_config, **loaded["viz_config"]})
                st.success("✅ 配置已加载成功！请根据需要重新拉取数据或运行清洗代码。")
            except Exception as e:
                st.error(f"❌ 加载配置失败：{e}")
    
    # 重置功能
    st.markdown("---")
    st.markdown("##### 🔄 重置选项")
    
    reset_col1, reset_col2, reset_col3 = st.columns(3)
    
    with reset_col1:
        if st.button("🗑️ 清空数据", help="清除所有导入的数据"):
            set_session_state("datasets", {})
            set_session_state("current_table", None)
            st.success("数据已清空")
            st.rerun()
    
    with reset_col2:
        if st.button("🧹 重置清洗", help="清空清洗代码和清洗结果"):
            set_session_state("clean_code", "")
            current_table = get_session_state("current_table")
            if current_table:
                datasets = get_session_state("datasets", {})
                if current_table in datasets:
                    datasets[current_table].pop("clean", None)
                    set_session_state("datasets", datasets)
            st.success("清洗设置已重置")
            st.rerun()
    
    with reset_col3:
        if st.button("⚠️ 重置全部", help="重置所有配置和数据", type="secondary"):
            from utils.session_state import clear_session_state
            clear_session_state()
            st.success("所有设置已重置")
            st.rerun()