import streamlit as st
import pandas as pd
import altair as alt
import json
from datetime import datetime
from utils.session_state import get_session_state, set_session_state
from utils.chart_utils import create_chart_from_config

def show():
    """显示数据看板页面"""
    # 页面头部
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.title("📊 数据看板")
        st.caption("拖拽式图表布局，实时数据展示")
    
    with col2:
        if st.button("🔧 进入工作台", type="primary", use_container_width=True, key="goto_workspace"):
            # 设置会话状态以切换到工作台页面
            st.session_state['nav_selection'] = '数据分析工作台'
            # 设置一个标志确保页面切换
            st.session_state['page_switch_requested'] = True
            st.rerun()
    
    # 获取看板图表
    dashboard_charts = get_session_state("dashboard_charts", [])
    last_update_time = get_session_state("last_update_time")
    
    # 显示更新时间
    if last_update_time:
        st.info(f"📅 数据最后更新时间: {last_update_time}")
    
    # 如果没有图表，显示提示
    if not dashboard_charts:
        st.info("📈 暂无图表数据。请前往【数据分析工作台】创建图表并添加到看板。")
        
        # 显示示例说明
        with st.expander("💡 如何使用数据看板", expanded=True):
            st.markdown("""
            ### 使用步骤：
            1. 点击右上角【🔧 进入工作台】按钮
            2. 在工作台中导入数据（CSV、Excel、API或Notion）
            3. 进行数据清洗（可选）
            4. 创建可视化图表
            5. 点击【📌 添加到看板】将图表保存到看板
            6. 返回看板查看所有图表
            
            ### 看板功能：
            - 🔄 每个图表右上角有刷新按钮，可重新加载数据
            - 📐 支持拖拽调整图表位置和大小（开发中）
            - 📊 实时显示数据更新状态
            """)
        return
    
    # 显示图表网格
    st.markdown("### 📊 图表展示")
    
    # 创建图表网格布局
    cols_per_row = 2  # 每行显示2个图表
    
    for i in range(0, len(dashboard_charts), cols_per_row):
        cols = st.columns(cols_per_row)
        
        for j in range(cols_per_row):
            chart_idx = i + j
            if chart_idx < len(dashboard_charts):
                chart_info = dashboard_charts[chart_idx]
                
                with cols[j]:
                    render_chart_module(chart_info, chart_idx)

def render_chart_module(chart_info, chart_idx):
    """渲染单个图表模块"""
    with st.container(border=True):
        # 图表头部
        col1, col2 = st.columns([3, 1])
        
        with col1:
            st.subheader(chart_info.get('title', f'图表 {chart_idx + 1}'))
            if chart_info.get('description'):
                st.caption(chart_info['description'])
        
        with col2:
            # 刷新按钮
            if st.button("🔄", key=f"refresh_{chart_idx}", help="刷新图表数据"):
                refresh_chart_data(chart_idx)
            
            # 删除按钮
            if st.button("🗑️", key=f"delete_{chart_idx}", help="删除图表"):
                delete_chart_from_dashboard(chart_idx)
                st.rerun()
        
        # 显示图表
        try:
            # 获取当前数据（兼容新的多表系统）
            current_table = get_session_state("current_table")
            datasets = get_session_state("datasets", {})
            
            active_df = None
            
            # 优先使用多表系统的数据
            if current_table and current_table in datasets:
                dataset = datasets[current_table]
                clean_df = dataset.get("clean")
                raw_df = dataset.get("raw")
                
                if clean_df is not None and not clean_df.empty:
                    active_df = clean_df
                elif raw_df is not None and not raw_df.empty:
                    active_df = raw_df
            
            # 如果多表系统没有数据，回退到旧的单表系统
            if active_df is None:
                clean_df = get_session_state("clean_df")
                raw_df = get_session_state("raw_df")
                
                if clean_df is not None and not clean_df.empty:
                    active_df = clean_df
                elif raw_df is not None and not raw_df.empty:
                    active_df = raw_df
            
            if active_df is None or active_df.empty:
                st.warning("⚠️ 无可用数据，请重新导入数据")
                return
            
            # 验证图表配置与当前数据的兼容性
            chart_config = chart_info.get('config', {})
            x_col = chart_config.get('x')
            y_col = chart_config.get('y')
            
            # 检查配置的列是否存在于当前数据中
            missing_cols = []
            if x_col and x_col not in active_df.columns:
                missing_cols.append(f"X轴: {x_col}")
            if y_col and y_col not in active_df.columns:
                missing_cols.append(f"Y轴: {y_col}")
            
            if missing_cols:
                st.error(f"图表配置与当前数据不匹配，缺少列: {', '.join(missing_cols)}")
                st.info(f"当前数据列: {', '.join(active_df.columns.tolist())}")
                return
            
            # 创建图表
            chart = create_chart_from_config(active_df, chart_config)
            
            if chart:
                st.altair_chart(chart, use_container_width=True)
            else:
                st.error("图表创建失败")
                
        except Exception as e:
            st.error(f"图表渲染失败: {str(e)}")
            # 显示详细错误信息用于调试
            with st.expander("查看详细错误信息"):
                st.code(str(e))
            
        # 显示图表信息
        with st.expander("📋 图表信息", expanded=False):
            st.json({
                "图表类型": chart_info.get('config', {}).get('chart_type', '未知'),
                "X轴": chart_info.get('config', {}).get('x', '未设置'),
                "Y轴": chart_info.get('config', {}).get('y', '未设置'),
                "创建时间": chart_info.get('created_at', '未知'),
                "数据行数": len(active_df) if 'active_df' in locals() else 0
            })

def refresh_chart_data(chart_idx):
    """刷新图表数据"""
    try:
        # 根据数据源类型重新获取数据
        source_type = get_session_state("source_type")
        
        if source_type == "api":
            # 重新拉取API数据
            from utils.data_utils import fetch_api_all, apply_clean_code
            
            api_config = get_session_state("api_config")
            df_raw, _metas = fetch_api_all(api_config)
            set_session_state("raw_df", df_raw)
            
            # 应用清洗代码
            clean_code = get_session_state("clean_code", "")
            if clean_code.strip():
                df_clean, logs, err = apply_clean_code(df_raw, clean_code)
                if not err:
                    set_session_state("clean_df", df_clean)
            else:
                set_session_state("clean_df", df_raw.copy())
                
        elif source_type == "notion":
            # 重新拉取Notion数据
            from utils.data_utils import fetch_notion_database, apply_clean_code
            
            notion_config = get_session_state("notion_config")
            df_raw = fetch_notion_database(notion_config)
            set_session_state("raw_df", df_raw)
            
            # 应用清洗代码
            clean_code = get_session_state("clean_code", "")
            if clean_code.strip():
                df_clean, logs, err = apply_clean_code(df_raw, clean_code)
                if not err:
                    set_session_state("clean_df", df_clean)
            else:
                set_session_state("clean_df", df_raw.copy())
        
        # 更新时间戳
        set_session_state("last_update_time", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        st.success(f"图表 {chart_idx + 1} 数据已刷新")
        st.rerun()
        
    except Exception as e:
        st.error(f"刷新失败: {str(e)}")

def delete_chart_from_dashboard(chart_idx):
    """从看板删除图表"""
    dashboard_charts = get_session_state("dashboard_charts", [])
    if 0 <= chart_idx < len(dashboard_charts):
        removed_chart = dashboard_charts.pop(chart_idx)
        set_session_state("dashboard_charts", dashboard_charts)
        st.success(f"已删除图表: {removed_chart.get('title', f'图表 {chart_idx + 1}')}")

def add_chart_to_dashboard(chart_config, title=None, description=None):
    """添加图表到看板"""
    dashboard_charts = get_session_state("dashboard_charts", [])
    
    chart_info = {
        "config": chart_config,
        "title": title or f"图表 {len(dashboard_charts) + 1}",
        "description": description or "",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "id": len(dashboard_charts)  # 简单的ID生成
    }
    
    dashboard_charts.append(chart_info)
    set_session_state("dashboard_charts", dashboard_charts)
    set_session_state("last_update_time", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    return len(dashboard_charts) - 1  # 返回新图表的索引