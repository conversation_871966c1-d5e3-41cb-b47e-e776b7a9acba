import streamlit as st
from streamlit_option_menu import option_menu
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入页面模块
from my_pages import dashboard, workspace
from utils.session_state import init_session_state

# 页面配置
st.set_page_config(
    page_title="数据分析平台",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 隐藏默认的页面导航
st.markdown("""
<style>
    .stAppHeader {display: none;}
    section[data-testid="stSidebar"] > div:first-child {
        padding-top: 1rem;
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
init_session_state()

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        padding: 1rem 0;
        border-bottom: 1px solid #e0e0e0;
        margin-bottom: 2rem;
    }
    .sidebar .sidebar-content {
        padding-top: 2rem;
    }
    .chart-container {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        background: white;
    }
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 侧边栏导航
with st.sidebar:
    st.title("📊 数据分析平台")
    
    # 检查会话状态中的导航选择
    nav_selection = st.session_state.get('nav_selection', '数据看板')
    
    # 处理页面切换请求
    if st.session_state.get('page_switch_requested', False):
        st.session_state['page_switch_requested'] = False
        # 强制更新导航选择
        nav_selection = st.session_state.get('nav_selection', '数据看板')
        # 强制重新运行以更新UI
        st.rerun()
    
    options = ["数据看板", "数据分析工作台", "设置"]
    default_index = options.index(nav_selection) if nav_selection in options else 0
    
    selected = option_menu(
        menu_title=None,
        options=options,
        icons=["bar-chart", "tools", "gear"],
        menu_icon="cast",
        default_index=default_index,
        key="main_nav",  # 添加key以避免重复渲染问题
        styles={
            "container": {"padding": "0!important", "background-color": "#fafafa"},
            "icon": {"color": "orange", "font-size": "18px"},
            "nav-link": {
                "font-size": "16px",
                "text-align": "left",
                "margin": "0px",
                "--hover-color": "#eee",
            },
            "nav-link-selected": {"background-color": "#02ab21"},
        },
    )
    
    # 更新会话状态
    st.session_state['nav_selection'] = selected

# 根据选择显示不同页面
if selected == "数据看板":
    dashboard.show()
elif selected == "数据分析工作台":
    workspace.show()
elif selected == "设置":
    st.header("⚙️ 设置")
    st.info("设置页面开发中...")
    
    # 基础设置选项
    with st.expander("🎨 界面设置", expanded=False):
        theme = st.selectbox("主题", ["默认", "深色", "浅色"])
        language = st.selectbox("语言", ["中文", "English"])
        
    with st.expander("📊 数据设置", expanded=False):
        auto_refresh = st.checkbox("自动刷新数据")
        if auto_refresh:
            refresh_interval = st.slider("刷新间隔（分钟）", 1, 60, 5)
        
        cache_size = st.slider("缓存大小（MB）", 10, 1000, 100)
        
    with st.expander("🔒 安全设置", expanded=False):
        st.warning("安全设置功能开发中...")
        enable_auth = st.checkbox("启用身份验证", disabled=True)
        session_timeout = st.slider("会话超时（小时）", 1, 24, 8, disabled=True)