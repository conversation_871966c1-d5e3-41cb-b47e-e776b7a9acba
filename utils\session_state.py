import streamlit as st

def init_session_state():
    """初始化会话状态"""
    defaults = {
        # 数据源相关
        "source_type": None,           # 'file' | 'api' | 'notion'
        "file_type": None,             # 'csv' | 'excel'
        "csv_sep": ",",
        "csv_encoding": "utf-8",
        "excel_sheet": None,
        "datasets": {},               # 存储多个数据表 {table_name: {"raw": df, "clean": df, "source_info": {}}}
        "current_table": None,        # 当前选中的表名
        "raw_df": None,               # 向后兼容，指向当前表的raw数据
        "clean_df": None,             # 向后兼容，指向当前表的clean数据
        "clean_code": "",
        
        # API配置
        "api_config": {
            "method": "GET",
            "url": "",
            "headers_json": "{}",
            "params_json": "{}",
            "body_json": "{}",
            "records_path": "",        # e.g. "data.items"
            "enable_pagination": False,
            "page_param": "page",
            "page_start": 1,
            "page_size_param": "page_size",
            "page_size_value": 100,
            "max_pages": 10,
            "timeout": 30,
            "auth_type": "None",
            "auth_bearer_token": "",
            "auth_basic_user": "",
            "auth_basic_pass": "",
        },
        
        # Notion配置
        "notion_config": {
            "token": "",
            "database_id": "",
            "max_results": 100,
            "filter_json": "{}",
            "sorts_json": "[]",
        },
        
        # 可视化配置
        "viz_config": {
            "chart_type": "折线图",
            "x": None,
            "y": None,
            "color": None,
            "size": None,
            "aggregate": "none",
            "tooltip": [],
            "sample_rows": 5000,
        },
        
        # 数据看板相关
        "dashboard_charts": [],  # 存储添加到看板的图表
        "dashboard_layout": {},  # 存储看板布局信息
        "last_update_time": None,  # 最后更新时间
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

def get_session_state(key, default=None):
    """获取会话状态值"""
    return st.session_state.get(key, default)

def set_session_state(key, value):
    """设置会话状态值"""
    st.session_state[key] = value

def add_dataset(table_name: str, raw_df, source_info: dict = None):
    """添加新的数据表"""
    if "datasets" not in st.session_state:
        st.session_state["datasets"] = {}
    
    st.session_state["datasets"][table_name] = {
        "raw": raw_df,
        "clean": raw_df.copy(),  # 初始时clean数据与raw相同
        "source_info": source_info or {}
    }
    
    # 设置为当前表
    set_current_table(table_name)

def set_current_table(table_name: str):
    """设置当前选中的表"""
    st.session_state["current_table"] = table_name
    
    # 更新向后兼容的变量
    if table_name and table_name in st.session_state.get("datasets", {}):
        dataset = st.session_state["datasets"][table_name]
        st.session_state["raw_df"] = dataset["raw"]
        st.session_state["clean_df"] = dataset["clean"]
    else:
        st.session_state["raw_df"] = None
        st.session_state["clean_df"] = None

def get_dataset_names():
    """获取所有数据表名称"""
    return list(st.session_state.get("datasets", {}).keys())

def update_clean_data(table_name: str, clean_df):
    """更新指定表的清洗后数据"""
    if table_name in st.session_state.get("datasets", {}):
        st.session_state["datasets"][table_name]["clean"] = clean_df
        
        # 如果是当前表，同时更新向后兼容变量
        if st.session_state.get("current_table") == table_name:
            st.session_state["clean_df"] = clean_df

def clear_session_state():
    """清空会话状态"""
    for key in list(st.session_state.keys()):
        if not key.startswith('_'):  # 保留streamlit内部状态
            del st.session_state[key]
    init_session_state()