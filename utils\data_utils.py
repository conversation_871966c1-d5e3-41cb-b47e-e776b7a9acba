import pandas as pd
import requests
import json
import io
from typing import Dict, <PERSON>, Tu<PERSON>, List, Optional
import re
from datetime import datetime

def parse_json_input(json_str: str) -> Dict[str, Any]:
    """解析JSON输入，支持空字符串"""
    if not json_str or json_str.strip() == "":
        return {}
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        raise ValueError(f"JSON格式错误: {e}")

def get_nested_value(data: Dict[str, Any], path: str) -> Any:
    """根据点号分隔的路径获取嵌套字典的值"""
    if not path or not path.strip():
        return data
    
    keys = path.split('.')
    current = data
    
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        elif isinstance(current, list) and key.isdigit():
            idx = int(key)
            if 0 <= idx < len(current):
                current = current[idx]
            else:
                return None
        else:
            return None
    
    return current

def fetch_api_single(config: Dict[str, Any], page: int = None) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """执行单次API请求"""
    url = config.get("url", "")
    method = config.get("method", "GET").upper()
    timeout = config.get("timeout", 30)
    
    # 解析JSON配置
    headers = parse_json_input(config.get("headers_json", "{}"))
    params = parse_json_input(config.get("params_json", "{}"))
    body = parse_json_input(config.get("body_json", "{}")) if method == "POST" else None
    
    # 添加分页参数
    if page is not None and config.get("enable_pagination", False):
        page_param = config.get("page_param", "page")
        page_size_param = config.get("page_size_param", "page_size")
        page_size_value = config.get("page_size_value", 100)
        
        if method == "GET":
            params[page_param] = page
            params[page_size_param] = page_size_value
        elif method == "POST" and body is not None:
            body[page_param] = page
            body[page_size_param] = page_size_value
    
    # 设置认证
    auth = None
    auth_type = config.get("auth_type", "None")
    if auth_type == "Bearer":
        token = config.get("auth_bearer_token", "")
        if token:
            headers["Authorization"] = f"Bearer {token}"
    elif auth_type == "Basic":
        username = config.get("auth_basic_user", "")
        password = config.get("auth_basic_pass", "")
        if username and password:
            from requests.auth import HTTPBasicAuth
            auth = HTTPBasicAuth(username, password)
    
    # 执行请求
    if method == "GET":
        response = requests.get(url, headers=headers, params=params, auth=auth, timeout=timeout)
    elif method == "POST":
        response = requests.post(url, headers=headers, params=params, json=body, auth=auth, timeout=timeout)
    else:
        raise ValueError(f"不支持的HTTP方法: {method}")
    
    response.raise_for_status()
    
    # 解析响应
    try:
        data = response.json()
    except json.JSONDecodeError:
        raise ValueError("API响应不是有效的JSON格式")
    
    # 提取记录数据
    records_path = config.get("records_path", "")
    if records_path:
        records = get_nested_value(data, records_path)
        if records is None:
            raise ValueError(f"无法在响应中找到路径: {records_path}")
    else:
        records = data
    
    # 确保records是列表
    if not isinstance(records, list):
        if isinstance(records, dict):
            records = [records]
        else:
            raise ValueError(f"提取的数据不是列表或字典格式: {type(records)}")
    
    return records, {"page": page, "total_records": len(records), "response_size": len(response.text)}

def fetch_api_all(config: Dict[str, Any]) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
    """获取所有API数据（支持分页）"""
    all_records = []
    all_metas = []
    
    if config.get("enable_pagination", False):
        page_start = config.get("page_start", 1)
        max_pages = config.get("max_pages", 10)
        
        for page_num in range(page_start, page_start + max_pages):
            try:
                records, meta = fetch_api_single(config, page_num)
                all_records.extend(records)
                all_metas.append(meta)
                
                # 如果返回的记录数少于预期，可能已到最后一页
                if len(records) < config.get("page_size_value", 100):
                    break
                    
            except Exception as e:
                if page_num == page_start:
                    # 第一页就失败，抛出异常
                    raise e
                else:
                    # 后续页面失败，可能是到了最后一页，停止分页
                    break
    else:
        # 不分页，单次请求
        records, meta = fetch_api_single(config)
        all_records.extend(records)
        all_metas.append(meta)
    
    if not all_records:
        raise ValueError("API返回的数据为空")
    
    # 转换为DataFrame
    df = pd.json_normalize(all_records)
    
    return df, all_metas

def extract_database_id_from_url(url: str) -> str:
    """从Notion数据库URL中提取Database ID"""
    if not url:
        raise ValueError("请提供Notion数据库URL")
    
    # 匹配各种Notion URL格式
    patterns = [
        r'notion\.so/([a-f0-9]{32})',  # https://www.notion.so/database_id
        r'notion\.so/.+/([a-f0-9]{32})',  # https://www.notion.so/workspace/database_id
        r'([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})',  # UUID格式
        r'([a-f0-9]{32})',  # 纯32位ID
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url, re.IGNORECASE)
        if match:
            database_id = match.group(1).replace('-', '').lower()
            if len(database_id) == 32:
                return database_id
    
    raise ValueError("无法从URL中提取有效的Database ID")

def fetch_notion_database(config: Dict[str, Any]) -> pd.DataFrame:
    """从Notion数据库获取数据"""
    try:
        from notion_client import Client
    except ImportError:
        raise ImportError("请安装notion-client: pip install notion-client")
    
    token = config.get("token", "")
    database_id = config.get("database_id", "")
    max_results = config.get("max_results", 100)
    
    if not token:
        raise ValueError("请提供Notion Token")
    if not database_id:
        raise ValueError("请提供Database ID")
    
    # 清理database_id（移除URL中的其他部分）
    database_id = re.sub(r'[^a-f0-9]', '', database_id.lower())
    if len(database_id) != 32:
        raise ValueError(f"Database ID格式错误，应为32位字符串，当前长度: {len(database_id)}")
    
    # 初始化Notion客户端
    notion = Client(auth=token)
    
    try:
        # 查询数据库
        response = notion.databases.query(
            database_id=database_id,
            page_size=min(max_results, 100)  # Notion API限制每次最多100条
        )
        
        results = response.get("results", [])
        
        # 如果需要更多数据且有下一页
        while response.get("has_more", False) and len(results) < max_results:
            response = notion.databases.query(
                database_id=database_id,
                page_size=min(max_results - len(results), 100),
                start_cursor=response.get("next_cursor")
            )
            results.extend(response.get("results", []))
        
        if not results:
            raise ValueError("Notion数据库为空或无权限访问")
        
        # 解析Notion数据
        parsed_data = []
        for page in results:
            row = {"id": page.get("id", "")}
            
            # 解析属性
            properties = page.get("properties", {})
            for prop_name, prop_data in properties.items():
                prop_type = prop_data.get("type", "")
                
                if prop_type == "title":
                    title_list = prop_data.get("title", [])
                    row[prop_name] = "".join([t.get("plain_text", "") for t in title_list])
                elif prop_type == "rich_text":
                    rich_text_list = prop_data.get("rich_text", [])
                    row[prop_name] = "".join([t.get("plain_text", "") for t in rich_text_list])
                elif prop_type == "number":
                    row[prop_name] = prop_data.get("number")
                elif prop_type == "select":
                    select_obj = prop_data.get("select")
                    row[prop_name] = select_obj.get("name", "") if select_obj else ""
                elif prop_type == "multi_select":
                    multi_select_list = prop_data.get("multi_select", [])
                    row[prop_name] = ", ".join([ms.get("name", "") for ms in multi_select_list])
                elif prop_type == "date":
                    date_obj = prop_data.get("date")
                    if date_obj:
                        row[prop_name] = date_obj.get("start", "")
                    else:
                        row[prop_name] = ""
                elif prop_type == "checkbox":
                    row[prop_name] = prop_data.get("checkbox", False)
                elif prop_type == "url":
                    row[prop_name] = prop_data.get("url", "")
                elif prop_type == "email":
                    row[prop_name] = prop_data.get("email", "")
                elif prop_type == "phone_number":
                    row[prop_name] = prop_data.get("phone_number", "")
                elif prop_type == "created_time":
                    row[prop_name] = prop_data.get("created_time", "")
                elif prop_type == "last_edited_time":
                    row[prop_name] = prop_data.get("last_edited_time", "")
                else:
                    # 对于不支持的类型，尝试转换为字符串
                    row[prop_name] = str(prop_data.get(prop_type, ""))
            
            parsed_data.append(row)
        
        df = pd.DataFrame(parsed_data)
        
        if df.empty:
            raise ValueError("解析Notion数据后结果为空")
        
        return df
        
    except Exception as e:
        if "Unauthorized" in str(e):
            raise ValueError("Notion Token无效或无权限访问该数据库")
        elif "not_found" in str(e):
            raise ValueError("找不到指定的Notion数据库，请检查Database ID")
        else:
            raise ValueError(f"Notion API错误: {e}")

def apply_clean_code(df: pd.DataFrame, code: str) -> Tuple[pd.DataFrame, str, Optional[str]]:
    """安全执行数据清洗代码"""
    if not code or not code.strip():
        return df.copy(), "", None
    
    # 创建安全的执行环境
    safe_globals = {
        "pd": pd,
        "datetime": datetime,
        "re": re,
        "json": json,
        "len": len,
        "str": str,
        "int": int,
        "float": float,
        "list": list,
        "dict": dict,
        "set": set,
        "tuple": tuple,
        "range": range,
        "enumerate": enumerate,
        "zip": zip,
        "map": map,
        "filter": filter,
        "sorted": sorted,
        "sum": sum,
        "max": max,
        "min": min,
        "abs": abs,
        "round": round,
    }
    
    # 禁用危险的内置函数和模块
    dangerous_names = [
        "__import__", "eval", "exec", "compile", "open", "file",
        "input", "raw_input", "reload", "__builtins__",
        "globals", "locals", "vars", "dir", "hasattr", "getattr", "setattr", "delattr",
        "os", "sys", "subprocess", "importlib"
    ]
    
    for name in dangerous_names:
        if name in safe_globals:
            del safe_globals[name]
    
    # 检查代码中是否包含危险操作
    dangerous_patterns = [
        r'\b__import__\b', r'\beval\b', r'\bexec\b', r'\bcompile\b',
        r'\bopen\b', r'\bfile\b', r'\binput\b', r'\braw_input\b',
        r'\bos\.', r'\bsys\.', r'\bsubprocess\.',
        r'\bglobals\b', r'\blocals\b', r'\bvars\b',
        r'\b__.*__\b'  # 双下划线方法
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, code, re.IGNORECASE):
            return df, "", f"代码包含潜在危险操作: {pattern}"
    
    # 创建本地变量环境
    local_vars = {"df": df.copy()}
    
    # 捕获输出
    output_buffer = io.StringIO()
    
    try:
        # 重定向print输出
        import sys
        old_stdout = sys.stdout
        sys.stdout = output_buffer
        
        # 执行代码
        exec(code, safe_globals, local_vars)
        
        # 恢复输出
        sys.stdout = old_stdout
        
        # 获取结果
        if "result" in local_vars:
            result_df = local_vars["result"]
        elif "clean_df" in local_vars:
            result_df = local_vars["clean_df"]
        else:
            result_df = local_vars["df"]
        
        # 验证结果是DataFrame
        if not isinstance(result_df, pd.DataFrame):
            return df, output_buffer.getvalue(), "执行结果不是DataFrame类型"
        
        logs = output_buffer.getvalue()
        return result_df, logs, None
        
    except Exception as e:
        # 恢复输出
        sys.stdout = old_stdout
        logs = output_buffer.getvalue()
        return df, logs, f"代码执行错误: {str(e)}"

def df_to_excel_bytes(df: pd.DataFrame) -> bytes:
    """将DataFrame转换为Excel字节数据"""
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Data')
    return output.getvalue()

def convert_csv_encoding(file_content: bytes, from_encoding: str, to_encoding: str = "utf-8") -> bytes:
    """转换CSV文件编码"""
    try:
        # 解码原始内容
        text_content = file_content.decode(from_encoding)
        # 重新编码
        return text_content.encode(to_encoding)
    except Exception as e:
        raise ValueError(f"编码转换失败: {e}")

def detect_csv_encoding(file_content: bytes) -> str:
    """检测CSV文件编码"""
    encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp936', 'latin1', 'iso-8859-1']
    
    for encoding in encodings_to_try:
        try:
            file_content.decode(encoding)
            return encoding
        except UnicodeDecodeError:
            continue
    
    return 'utf-8'  # 默认返回utf-8