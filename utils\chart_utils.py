import altair as alt
import pandas as pd
from typing import Dict, Any, Optional

def create_chart_from_config(df: pd.DataFrame, config: Dict[str, Any]) -> Optional[alt.Chart]:
    """根据配置创建Altair图表"""
    if df is None or df.empty:
        return None
    
    chart_type = config.get("chart_type", "折线图")
    x_col = config.get("x")
    y_col = config.get("y")
    color_col = config.get("color")
    size_col = config.get("size")
    aggregate = config.get("aggregate", "none")
    tooltip = config.get("tooltip", [])
    sample_rows = config.get("sample_rows", 5000)
    
    # 数据采样
    if len(df) > sample_rows:
        df_chart = df.sample(n=sample_rows, random_state=42)
    else:
        df_chart = df.copy()
    
    # 基础图表对象
    base = alt.Chart(df_chart)
    
    # 构建编码
    encoding = {}
    
    if x_col:
        encoding['x'] = alt.X(x_col, title=x_col)
    if y_col:
        if aggregate != "none" and chart_type == "柱状图":
            encoding['y'] = alt.Y(f"{aggregate}({y_col})", title=f"{aggregate}({y_col})")
        else:
            encoding['y'] = alt.Y(y_col, title=y_col)
    
    if color_col:
        encoding['color'] = alt.Color(color_col, title=color_col)
    
    if size_col and chart_type == "散点图":
        encoding['size'] = alt.Size(size_col, title=size_col)
    
    # 设置tooltip
    if tooltip:
        encoding['tooltip'] = tooltip
    elif x_col and y_col:
        encoding['tooltip'] = [x_col, y_col]
    
    # 根据图表类型创建图表
    if chart_type == "折线图":
        chart = base.mark_line(point=True).add_selection(
            alt.selection_interval(bind='scales')
        )
    elif chart_type == "柱状图":
        chart = base.mark_bar().add_selection(
            alt.selection_interval(bind='scales')
        )
    elif chart_type == "散点图":
        chart = base.mark_circle().add_selection(
            alt.selection_interval(bind='scales')
        )
    elif chart_type == "直方图":
        if x_col:
            chart = base.mark_bar().add_selection(
                alt.selection_interval(bind='scales')
            )
            encoding['x'] = alt.X(f"{x_col}:O", bin=True, title=x_col)
            encoding['y'] = alt.Y('count()', title='频次')
        else:
            return None
    elif chart_type == "箱线图":
        if x_col and y_col:
            chart = base.mark_boxplot().add_selection(
                alt.selection_interval(bind='scales')
            )
        else:
            return None
    else:
        return None
    
    # 应用编码
    chart = chart.encode(**encoding)
    
    # 设置图表属性
    chart = chart.properties(
        width=600,
        height=400,
        title=f"{chart_type}: {x_col} vs {y_col}" if x_col and y_col else chart_type
    ).resolve_scale(
        color='independent'
    )
    
    return chart

def get_chart_summary(config: Dict[str, Any]) -> str:
    """获取图表配置摘要"""
    chart_type = config.get("chart_type", "未知")
    x_col = config.get("x", "未设置")
    y_col = config.get("y", "未设置")
    color_col = config.get("color")
    aggregate = config.get("aggregate", "none")
    
    summary = f"{chart_type} | X: {x_col} | Y: {y_col}"
    
    if color_col:
        summary += f" | 分组: {color_col}"
    
    if aggregate != "none" and chart_type == "柱状图":
        summary += f" | 聚合: {aggregate}"
    
    return summary

def validate_chart_config(df: pd.DataFrame, config: Dict[str, Any]) -> tuple[bool, str]:
    """验证图表配置是否有效"""
    if df is None or df.empty:
        return False, "数据为空"
    
    chart_type = config.get("chart_type")
    x_col = config.get("x")
    y_col = config.get("y")
    
    if not chart_type:
        return False, "未选择图表类型"
    
    if chart_type in ["折线图", "柱状图", "散点图", "箱线图"]:
        if not x_col or not y_col:
            return False, f"{chart_type}需要设置X轴和Y轴"
        
        if x_col not in df.columns:
            return False, f"列 '{x_col}' 不存在"
        
        if y_col not in df.columns:
            return False, f"列 '{y_col}' 不存在"
    
    elif chart_type == "直方图":
        if not x_col:
            return False, "直方图需要设置X轴"
        
        if x_col not in df.columns:
            return False, f"列 '{x_col}' 不存在"
    
    # 检查颜色列
    color_col = config.get("color")
    if color_col and color_col not in df.columns:
        return False, f"颜色列 '{color_col}' 不存在"
    
    # 检查大小列
    size_col = config.get("size")
    if size_col and size_col not in df.columns:
        return False, f"大小列 '{size_col}' 不存在"
    
    return True, "配置有效"